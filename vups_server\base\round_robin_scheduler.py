"""
Round-Robin Task Scheduler for VUPs Server

Implements a round-robin system that rotates between the three servers 
for continuous data pulling with cookie-based task management.
"""

import asyncio
import time
from abc import ABC, abstractmethod
from collections import deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Callable, Any, Set
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger

from vups.logger import logger
from vups_server.base.cookie_manager import get_cookie_manager


class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class TaskDefinition:
    """Definition of a data pulling task"""
    name: str
    server_type: str  # 'user', 'creator', 'live'
    function_name: str
    priority: TaskPriority = TaskPriority.NORMAL
    frequency_minutes: Optional[int] = None
    cron_schedule: Optional[str] = None
    max_execution_time: int = 300  # 5 minutes default
    retry_count: int = 3
    retry_delay: int = 60
    requires_cookie: bool = True
    dependencies: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TaskExecution:
    """Record of task execution"""
    task_name: str
    server_type: str
    start_time: datetime
    end_time: Optional[datetime] = None
    status: TaskStatus = TaskStatus.PENDING
    error_message: Optional[str] = None
    retry_count: int = 0
    execution_id: str = ""


class RoundRobinTaskScheduler:
    """
    Round-robin task scheduler with cookie-based task management
    
    Manages task distribution across multiple server types, handles
    cookie rotation, and ensures balanced load distribution.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        self.scheduler = AsyncIOScheduler()
        self.cookie_manager = get_cookie_manager()
        
        # Task management
        self.tasks: Dict[str, TaskDefinition] = {}
        self.task_queues: Dict[str, deque] = {
            'user': deque(),
            'creator': deque(), 
            'live': deque()
        }
        self.execution_history: List[TaskExecution] = []
        self.running_tasks: Dict[str, TaskExecution] = {}
        
        # Round-robin state
        self.server_rotation = deque(['user', 'creator', 'live'])
        self.last_execution_times: Dict[str, datetime] = {}
        self.server_load: Dict[str, int] = {'user': 0, 'creator': 0, 'live': 0}
        
        # Configuration
        self.max_concurrent_tasks = 3
        self.task_timeout = 300  # 5 minutes
        self.health_check_interval = 60  # 1 minute
        
        # Server instances (to be set by external code)
        self.server_instances: Dict[str, Any] = {}
        
        # State tracking
        self.is_running = False
        self.stats = {
            'total_tasks_executed': 0,
            'successful_tasks': 0,
            'failed_tasks': 0,
            'cookie_rotations': 0
        }
    
    def register_server(self, server_type: str, server_instance: Any):
        """Register a server instance for task execution"""
        self.server_instances[server_type] = server_instance
        logger.info(f"Registered {server_type} server instance")
    
    def add_task(self, task_def: TaskDefinition):
        """Add a task definition to the scheduler"""
        self.tasks[task_def.name] = task_def
        logger.info(f"Added task: {task_def.name} for {task_def.server_type} server")
    
    def remove_task(self, task_name: str):
        """Remove a task definition from the scheduler"""
        if task_name in self.tasks:
            del self.tasks[task_name]
            logger.info(f"Removed task: {task_name}")
    
    def get_next_server(self) -> str:
        """Get next server in round-robin rotation"""
        # Rotate to next server
        self.server_rotation.rotate(-1)
        next_server = self.server_rotation[0]
        
        # Check if server is available and has capacity
        if (self.server_load[next_server] < self.max_concurrent_tasks and 
            next_server in self.server_instances):
            return next_server
        
        # Find alternative server with capacity
        for server_type in ['user', 'creator', 'live']:
            if (self.server_load[server_type] < self.max_concurrent_tasks and 
                server_type in self.server_instances):
                return server_type
        
        # Return original if no alternatives (will queue)
        return next_server
    
    async def should_execute_task(self, task_def: TaskDefinition) -> bool:
        """Check if task should be executed based on frequency and dependencies"""
        current_time = datetime.now()
        
        # Check frequency constraint
        if task_def.frequency_minutes:
            last_execution = self.last_execution_times.get(task_def.name)
            if last_execution:
                time_since_last = (current_time - last_execution).total_seconds() / 60
                if time_since_last < task_def.frequency_minutes:
                    return False
        
        # Check dependencies
        for dep_task_name in task_def.dependencies:
            dep_execution = next(
                (ex for ex in reversed(self.execution_history) 
                 if ex.task_name == dep_task_name), None
            )
            if not dep_execution or dep_execution.status != TaskStatus.COMPLETED:
                logger.debug(f"Task {task_def.name} waiting for dependency {dep_task_name}")
                return False
        
        # Check cookie availability
        if task_def.requires_cookie:
            cookie = self.cookie_manager.get_cookie_for_task(task_def.server_type)
            if not cookie:
                logger.warning(f"No cookie available for {task_def.server_type}, skipping {task_def.name}")
                return False
        
        return True
    
    async def execute_task(self, task_def: TaskDefinition, server_type: str) -> TaskExecution:
        """Execute a single task"""
        execution_id = f"{task_def.name}_{int(time.time())}"
        execution = TaskExecution(
            task_name=task_def.name,
            server_type=server_type,
            start_time=datetime.now(),
            execution_id=execution_id,
            status=TaskStatus.RUNNING
        )
        
        self.running_tasks[execution_id] = execution
        self.server_load[server_type] += 1
        
        try:
            logger.info(f"Executing task {task_def.name} on {server_type} server")
            
            # Get server instance
            server_instance = self.server_instances.get(server_type)
            if not server_instance:
                raise Exception(f"No server instance available for {server_type}")
            
            # Get task function
            task_function = getattr(server_instance, task_def.function_name, None)
            if not task_function:
                raise Exception(f"Function {task_def.function_name} not found on {server_type} server")
            
            # Execute with timeout
            result = await asyncio.wait_for(
                task_function(**task_def.metadata),
                timeout=task_def.max_execution_time
            )
            
            execution.status = TaskStatus.COMPLETED
            execution.end_time = datetime.now()
            
            self.last_execution_times[task_def.name] = execution.end_time
            self.stats['successful_tasks'] += 1
            
            logger.info(f"Task {task_def.name} completed successfully")
            
        except asyncio.TimeoutError:
            execution.status = TaskStatus.FAILED
            execution.error_message = f"Task timed out after {task_def.max_execution_time} seconds"
            execution.end_time = datetime.now()
            self.stats['failed_tasks'] += 1
            logger.error(f"Task {task_def.name} timed out")
            
        except Exception as e:
            execution.status = TaskStatus.FAILED
            execution.error_message = str(e)
            execution.end_time = datetime.now()
            self.stats['failed_tasks'] += 1
            logger.error(f"Task {task_def.name} failed: {e}")
            
        finally:
            # Cleanup
            if execution_id in self.running_tasks:
                del self.running_tasks[execution_id]
            self.server_load[server_type] -= 1
            self.execution_history.append(execution)
            self.stats['total_tasks_executed'] += 1
            
            # Keep only recent history (last 1000 executions)
            if len(self.execution_history) > 1000:
                self.execution_history = self.execution_history[-1000:]
        
        return execution
    
    async def process_task_queue(self):
        """Process pending tasks in round-robin fashion"""
        if not self.is_running:
            return
            
        # Collect eligible tasks
        eligible_tasks = []
        for task_name, task_def in self.tasks.items():
            if await self.should_execute_task(task_def):
                eligible_tasks.append(task_def)
        
        # Sort by priority
        eligible_tasks.sort(key=lambda t: t.priority.value, reverse=True)
        
        # Execute tasks in round-robin fashion
        for task_def in eligible_tasks:
            if len(self.running_tasks) >= self.max_concurrent_tasks:
                break
                
            server_type = self.get_next_server()
            if self.server_load[server_type] < self.max_concurrent_tasks:
                # Execute task asynchronously
                asyncio.create_task(self.execute_task(task_def, server_type))
    
    async def health_check(self):
        """Perform health checks and maintenance"""
        if not self.is_running:
            return
            
        current_time = datetime.now()
        
        # Check for stuck tasks
        for execution_id, execution in list(self.running_tasks.items()):
            if (current_time - execution.start_time).total_seconds() > self.task_timeout:
                logger.warning(f"Task {execution.task_name} appears stuck, marking as failed")
                execution.status = TaskStatus.FAILED
                execution.error_message = "Task exceeded maximum execution time"
                execution.end_time = current_time
                
                # Clean up
                del self.running_tasks[execution_id]
                self.server_load[execution.server_type] -= 1
                self.execution_history.append(execution)
        
        # Refresh cookies if needed
        try:
            await self.cookie_manager.refresh_all_cookies()
        except Exception as e:
            logger.error(f"Error during cookie refresh: {e}")
        
        # Log statistics
        logger.info(f"Scheduler stats: {self.stats}")
        logger.info(f"Running tasks: {len(self.running_tasks)}")
        logger.info(f"Server load: {self.server_load}")
    
    def start(self):
        """Start the round-robin scheduler"""
        if self.is_running:
            logger.warning("Scheduler is already running")
            return
            
        self.is_running = True
        
        # Add main processing job
        self.scheduler.add_job(
            self.process_task_queue,
            IntervalTrigger(seconds=30),
            id='task_processor',
            name='Task Queue Processor'
        )
        
        # Add health check job
        self.scheduler.add_job(
            self.health_check,
            IntervalTrigger(seconds=self.health_check_interval),
            id='health_check',
            name='Health Check'
        )
        
        self.scheduler.start()
        logger.info("Round-robin task scheduler started")
    
    def stop(self):
        """Stop the round-robin scheduler"""
        if not self.is_running:
            logger.warning("Scheduler is not running")
            return
            
        self.is_running = False
        self.scheduler.shutdown()
        logger.info("Round-robin task scheduler stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current scheduler status"""
        return {
            'is_running': self.is_running,
            'registered_servers': list(self.server_instances.keys()),
            'total_tasks': len(self.tasks),
            'running_tasks': len(self.running_tasks),
            'server_load': self.server_load.copy(),
            'stats': self.stats.copy(),
            'recent_executions': [
                {
                    'task_name': ex.task_name,
                    'server_type': ex.server_type,
                    'status': ex.status.value,
                    'start_time': ex.start_time.isoformat(),
                    'end_time': ex.end_time.isoformat() if ex.end_time else None
                }
                for ex in self.execution_history[-10:]  # Last 10 executions
            ]
        }
