"""
Usage Example for VUPs Round-Robin System

Demonstrates how to use the refactored VUPs server system with
round-robin scheduling and cookie-based task management.
"""

import asyncio
import json
from datetime import datetime

from vups.logger import logger
from vups_server.integration.round_robin_integration import get_system
from vups_server.base.round_robin_scheduler import TaskDefinition, TaskPriority


async def basic_usage_example():
    """Basic usage example"""
    logger.info("=== Basic Usage Example ===")
    
    # Get the system instance
    system = get_system({
        'creator': {
            'uid': '401315430',
            'enabled': True
        },
        'user': {
            'char': 'xingtong',
            'enabled': True
        },
        'live': {
            'enabled': True
        }
    })
    
    try:
        # Initialize the system
        logger.info("Initializing system...")
        await system.initialize()
        
        # Start the round-robin scheduler
        logger.info("Starting round-robin scheduler...")
        await system.start()
        
        # Check system status
        status = system.get_system_status()
        logger.info(f"System running with {len(status['servers'])} servers")
        logger.info(f"Registered tasks: {status['tasks']['registered_count']}")
        
        # Let it run for a bit
        logger.info("Running for 30 seconds...")
        await asyncio.sleep(30)
        
        # Check updated status
        updated_status = system.scheduler.get_status()
        logger.info(f"Tasks executed: {updated_status['stats']['total_tasks_executed']}")
        
    finally:
        # Stop the system
        logger.info("Stopping system...")
        await system.stop()


async def manual_task_execution_example():
    """Example of manual task execution"""
    logger.info("=== Manual Task Execution Example ===")
    
    system = get_system()
    
    try:
        await system.initialize()
        
        # Execute a creator info task manually
        logger.info("Executing creator overview stat task...")
        result = await system.execute_manual_task(
            'creator', 
            'fetch_overview_stat'
        )
        
        if result['success']:
            logger.info(f"Task completed in {result['execution_time']:.2f} seconds")
        else:
            logger.error(f"Task failed: {result['error']}")
        
        # Execute a user info task manually
        logger.info("Executing user current stat task...")
        result = await system.execute_manual_task(
            'user', 
            'fetch_user_current_stat'
        )
        
        if result['success']:
            logger.info(f"Task completed in {result['execution_time']:.2f} seconds")
        else:
            logger.error(f"Task failed: {result['error']}")
        
    except Exception as e:
        logger.error(f"Manual task execution failed: {e}")


async def health_monitoring_example():
    """Example of health monitoring"""
    logger.info("=== Health Monitoring Example ===")
    
    system = get_system()
    
    try:
        await system.initialize()
        
        # Perform health check
        health = await system.health_check()
        
        logger.info(f"Overall system health: {health['overall_status']}")
        
        # Check individual components
        for component, status in health['checks'].items():
            logger.info(f"{component}: {status['status']}")
            if status['status'] == 'error':
                logger.error(f"  Error: {status.get('error', 'Unknown error')}")
        
        # Monitor for a while
        logger.info("Monitoring health for 60 seconds...")
        for i in range(6):
            await asyncio.sleep(10)
            health = await system.health_check()
            logger.info(f"Health check {i+1}: {health['overall_status']}")
        
    except Exception as e:
        logger.error(f"Health monitoring failed: {e}")


async def custom_task_example():
    """Example of adding custom tasks"""
    logger.info("=== Custom Task Example ===")
    
    system = get_system()
    
    try:
        await system.initialize()
        
        # Add a custom task
        custom_task = TaskDefinition(
            name="custom_health_check",
            server_type="creator",
            function_name="get_server_status",
            priority=TaskPriority.LOW,
            frequency_minutes=5,
            requires_cookie=False
        )
        
        system.add_custom_task(custom_task)
        logger.info("Added custom health check task")
        
        # Start the system
        await system.start()
        
        # Let it run and execute the custom task
        logger.info("Running with custom task for 60 seconds...")
        await asyncio.sleep(60)
        
        # Check execution stats
        from vups_server.base.task_decorators import get_task_statistics
        stats = get_task_statistics()
        
        if 'custom_health_check' in stats:
            task_stats = stats['custom_health_check']
            logger.info(f"Custom task executed {task_stats['execution_count']} times")
            logger.info(f"Success rate: {task_stats['success_rate']:.2%}")
        
    except Exception as e:
        logger.error(f"Custom task example failed: {e}")
    finally:
        await system.stop()


async def cookie_rotation_example():
    """Example of cookie rotation functionality"""
    logger.info("=== Cookie Rotation Example ===")
    
    system = get_system()
    
    try:
        await system.initialize()
        
        # Check initial cookie status
        cookie_status = system.cookie_manager.get_status()
        logger.info("Initial cookie status:")
        for task_type, status in cookie_status['cookies'].items():
            logger.info(f"  {task_type}: enabled={status['enabled']}, needs_refresh={status['needs_refresh']}")
        
        # Refresh cookies for all enabled tasks
        enabled_tasks = cookie_status['enabled_tasks']
        for task_type in enabled_tasks:
            try:
                logger.info(f"Refreshing cookie for {task_type}...")
                success = await system.cookie_manager.refresh_cookie_for_task(task_type)
                logger.info(f"Cookie refresh for {task_type}: {'success' if success else 'failed'}")
            except Exception as e:
                logger.error(f"Failed to refresh cookie for {task_type}: {e}")
        
        # Check updated status
        updated_status = system.cookie_manager.get_status()
        logger.info("Updated cookie status:")
        for task_type, status in updated_status['cookies'].items():
            logger.info(f"  {task_type}: needs_refresh={status['needs_refresh']}")
        
    except Exception as e:
        logger.error(f"Cookie rotation example failed: {e}")


async def task_statistics_example():
    """Example of task statistics monitoring"""
    logger.info("=== Task Statistics Example ===")
    
    system = get_system()
    
    try:
        await system.initialize()
        await system.start()
        
        # Let the system run for a while
        logger.info("Running system to collect statistics...")
        await asyncio.sleep(120)  # 2 minutes
        
        # Get task statistics
        from vups_server.base.task_decorators import get_task_statistics
        stats = get_task_statistics()
        
        logger.info("Task execution statistics:")
        for task_name, task_stats in stats.items():
            logger.info(f"\n{task_name}:")
            logger.info(f"  Executions: {task_stats['execution_count']}")
            logger.info(f"  Errors: {task_stats['error_count']}")
            logger.info(f"  Success rate: {task_stats['success_rate']:.2%}")
            logger.info(f"  Avg execution time: {task_stats['average_execution_time']:.2f}s")
            if task_stats['last_execution']:
                logger.info(f"  Last execution: {task_stats['last_execution']}")
        
        # Get scheduler statistics
        scheduler_stats = system.scheduler.get_status()
        logger.info(f"\nScheduler statistics:")
        logger.info(f"  Total tasks executed: {scheduler_stats['stats']['total_tasks_executed']}")
        logger.info(f"  Successful tasks: {scheduler_stats['stats']['successful_tasks']}")
        logger.info(f"  Failed tasks: {scheduler_stats['stats']['failed_tasks']}")
        logger.info(f"  Cookie rotations: {scheduler_stats['stats']['cookie_rotations']}")
        
    except Exception as e:
        logger.error(f"Task statistics example failed: {e}")
    finally:
        await system.stop()


async def main():
    """Run all examples"""
    examples = [
        ("Basic Usage", basic_usage_example),
        ("Manual Task Execution", manual_task_execution_example),
        ("Health Monitoring", health_monitoring_example),
        ("Custom Task", custom_task_example),
        ("Cookie Rotation", cookie_rotation_example),
        ("Task Statistics", task_statistics_example),
    ]
    
    for example_name, example_func in examples:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running {example_name} Example")
        logger.info(f"{'='*60}")
        
        try:
            await example_func()
            logger.info(f"✅ {example_name} example completed successfully")
        except Exception as e:
            logger.error(f"❌ {example_name} example failed: {e}")
        
        # Wait between examples
        await asyncio.sleep(2)
    
    logger.info(f"\n{'='*60}")
    logger.info("All examples completed!")
    logger.info(f"{'='*60}")


if __name__ == "__main__":
    asyncio.run(main())
